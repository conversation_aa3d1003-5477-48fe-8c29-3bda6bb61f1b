# Bot Token - Loaded from .env file for security
import os
try:
    from dotenv import load_dotenv
    # Load environment variables from .env file
    load_dotenv()
    # Get token from environment variables
    TOKEN = os.getenv("BOT_TOKEN", "7896081390:AAG2h_TRvIzapusBY-OcsOt7gvfUMGw-ChQ")
except ImportError:
    # Fallback if python-dotenv is not installed
    TOKEN = "7896081390:AAG2h_TRvIzapusBY-OcsOt7gvfUMGw-ChQ"

# Database configuration
SQLITE_DB_PATH = "VL2024.sqlite"

# Support contact
SUPPORT_CONTACT = "@baglan_support"

# Admin IDs
ADMIN_IDS = [5421243466, 6404812540, 8046165511, 7440280333, 5702253746, 7772025660, 7772025660]  # Main admin and assistant admin
OWNER_ID = 5421243466  # Owner ID

# Search limits and points
FREE_SEARCHES_PER_DAY = 1
DEFAULT_SEARCH_POINTS = 1  # Default search points for new users

# Bot username for signature
BOT_USERNAME = "@baglan_gozleg_bot"  # Current bot username

# Owner contact
OWNER_CONTACT = "https://t.me/nerwa_degme"

# Channel ID for notifications
CHANNEL_ID = "-1002676767349"  # Change this to your channel username or ID

# Encoding/Decoding characters
ENCODE_CHARS = "1234567890-=\\\\@$^&()+| ;'',./{}:\"<>abcdefghijklmnopqrstuvwxyzабвгдеёжзийклмнопрстуфхцчшщъыьэюяABCDEFGHIJKLMNOPQRSTUVWXYZАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ"
DECODE_CHARS = ")|;/4&.$2:''=,+^>9-0<3178(@\"{ 65}\\\\rёмlйnfфvчежbыoитлрhюузбmцxоkiдdьgwхщzэqпtpuвaшъeyяcгаjнсsкRЁМLЙNFФVЧЕЖBЫOИТЛРHЮУЗБMЦXОKIДDЬGWХЩZЭQПTPUВAШЪEYЯCГАJНСSК"