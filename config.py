# Bot Token - Loaded from .env file for security
import os
try:
    from dotenv import load_dotenv
    # Load environment variables from .env file
    load_dotenv()
    # Get token from environment variables
    TOKEN = os.getenv("BOT_TOKEN", "7955134503:AAFw0Uv2BuOS5NuhSZagnj7eTucaKMcQTg0")
except ImportError:
    # Fallback if python-dotenv is not installed
    TOKEN = "7955134503:AAFw0Uv2BuOS5NuhSZagnj7eTucaKMcQTg0"

# Database configuration
SQLITE_DB_PATH = "VL2024.sqlite"

# Support contact
SUPPORT_CONTACT = "@TMCELLadmin"

# Admin IDs
ADMIN_IDS = [7027624995, 6687750461, 7772025660, 5421243466]  # Arslan_Vpns, rnxGG, TMCELLadmin, nerwa_degme
OWNER_ID = 7772025660  # Owner ID

# Search limits and points
FREE_SEARCHES_PER_DAY = 1
DEFAULT_SEARCH_POINTS = 1  # Default search points for new users

# Bot username for signature
BOT_USERNAME = "@tmcell993bot"  # Current bot username

# Owner contact
OWNER_CONTACT = "https://t.me/TMCELLadmin"

# Channel ID for notifications
CHANNEL_ID = "-1002676767349"  # Change this to your channel username or ID

# Encoding/Decoding characters
ENCODE_CHARS = "1234567890-=\\\\@$^&()+| ;'',./{}:\"<>abcdefghijklmnopqrstuvwxyzабвгдеёжзийклмнопрстуфхцчшщъыьэюяABCDEFGHIJKLMNOPQRSTUVWXYZАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ"
DECODE_CHARS = ")|;/4&.$2:''=,+^>9-0<3178(@\"{ 65}\\\\rёмlйnfфvчежbыoитлрhюузбmцxоkiдdьgwхщzэqпtpuвaшъeyяcгаjнсsкRЁМLЙNFФVЧЕЖBЫOИТЛРHЮУЗБMЦXОKIДDЬGWХЩZЭQПTPUВAШЪEYЯCГАJНСSК"